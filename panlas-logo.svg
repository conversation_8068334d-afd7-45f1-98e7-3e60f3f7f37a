<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2DD4BF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#14B8A6;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="8" stdDeviation="16" flood-color="#000000" flood-opacity="0.25"/>
    </filter>
  </defs>
  
  <!-- Background with rounded corners -->
  <rect x="32" y="32" width="448" height="448" rx="96" ry="96" fill="url(#bgGradient)" filter="url(#shadow)"/>
  
  <!-- Duck silhouette -->
  <g transform="translate(256,256)">
    <!-- Duck body -->
    <path d="M-80,-40 C-120,-40 -140,-20 -140,20 C-140,60 -120,80 -80,80 L60,80 C100,80 120,60 120,20 C120,-20 100,-40 60,-40 L-80,-40 Z" fill="#F7F3E9"/>
    
    <!-- Duck head -->
    <circle cx="-60" cy="-20" r="45" fill="#F7F3E9"/>
    
    <!-- Duck beak -->
    <path d="M-100,-25 L-120,-20 L-100,-15 Z" fill="#F7F3E9"/>
    
    <!-- Duck eye -->
    <circle cx="-70" cy="-30" r="6" fill="#14B8A6"/>
    
    <!-- Wing details -->
    <path d="M-20,0 C20,0 40,20 40,40 C40,60 20,70 -20,70 C-40,70 -50,50 -50,30 C-50,10 -40,0 -20,0 Z" fill="none" stroke="#14B8A6" stroke-width="4"/>
    <path d="M-10,20 C10,20 25,30 25,45 C25,55 10,60 -10,60 C-20,60 -25,50 -25,40 C-25,30 -20,20 -10,20 Z" fill="none" stroke="#14B8A6" stroke-width="3"/>
    <path d="M0,35 C15,35 25,40 25,50 C25,55 15,58 0,58 C-8,58 -12,53 -12,48 C-12,43 -8,35 0,35 Z" fill="none" stroke="#14B8A6" stroke-width="2"/>
  </g>
</svg>
